export class NoiseGenerator {
    constructor(seed = 12345) {
        this.seed = seed;
        this.permutation = this.generatePermutation();
    }
    
    generatePermutation() {
        // Create permutation table for Perlin noise
        const p = [];
        for (let i = 0; i < 256; i++) {
            p[i] = i;
        }
        
        // Shuffle using seed
        let random = this.seededRandom(this.seed);
        for (let i = 255; i > 0; i--) {
            const j = Math.floor(random() * (i + 1));
            [p[i], p[j]] = [p[j], p[i]];
        }
        
        // Duplicate for overflow
        for (let i = 0; i < 256; i++) {
            p[256 + i] = p[i];
        }
        
        return p;
    }
    
    seededRandom(seed) {
        let x = Math.sin(seed) * 10000;
        return function() {
            x = Math.sin(x) * 10000;
            return x - Math.floor(x);
        };
    }
    
    fade(t) {
        // Smoothstep function: 6t^5 - 15t^4 + 10t^3
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    lerp(a, b, t) {
        return a + t * (b - a);
    }
    
    grad(hash, x, y) {
        // Convert hash to gradient vector
        const h = hash & 3;
        const u = h < 2 ? x : y;
        const v = h < 2 ? y : x;
        return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
    }
    
    perlin(x, y) {
        // Find grid cell coordinates
        const X = Math.floor(x) & 255;
        const Y = Math.floor(y) & 255;
        
        // Find relative position in cell
        x -= Math.floor(x);
        y -= Math.floor(y);
        
        // Compute fade curves
        const u = this.fade(x);
        const v = this.fade(y);
        
        // Hash coordinates of 4 corners
        const A = this.permutation[X] + Y;
        const B = this.permutation[X + 1] + Y;
        
        // Blend the results from the 4 corners
        return this.lerp(
            this.lerp(
                this.grad(this.permutation[A], x, y),
                this.grad(this.permutation[B], x - 1, y),
                u
            ),
            this.lerp(
                this.grad(this.permutation[A + 1], x, y - 1),
                this.grad(this.permutation[B + 1], x - 1, y - 1),
                u
            ),
            v
        );
    }
    
    // Fractal Brownian Motion (fBm) - multiple octaves of noise
    fbm(x, y, octaves = 6, persistence = 0.5, lacunarity = 2.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += this.perlin(x * frequency, y * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return value / maxValue;
    }
    
    // Ridge noise - creates mountain ridges
    ridgeNoise(x, y, octaves = 6, persistence = 0.5, lacunarity = 2.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            let n = Math.abs(this.perlin(x * frequency, y * frequency));
            n = 1 - n; // Invert
            n = n * n; // Square for sharper ridges
            value += n * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return value / maxValue;
    }
    
    // Billow noise - creates cloud-like formations
    billowNoise(x, y, octaves = 6, persistence = 0.5, lacunarity = 2.0) {
        let value = 0;
        let amplitude = 1;
        let frequency = 1;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            let n = Math.abs(this.perlin(x * frequency, y * frequency));
            value += n * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return value / maxValue;
    }
    
    // Domain warping - distorts the coordinate space
    domainWarp(x, y, strength = 1.0) {
        const warpX = this.perlin(x * 0.1, y * 0.1) * strength;
        const warpY = this.perlin((x + 100) * 0.1, (y + 100) * 0.1) * strength;
        
        return {
            x: x + warpX,
            y: y + warpY
        };
    }
    
    // Voronoi noise - creates cellular patterns
    voronoi(x, y, scale = 1.0) {
        const cellX = Math.floor(x * scale);
        const cellY = Math.floor(y * scale);
        
        let minDist = Infinity;
        
        // Check 9 neighboring cells
        for (let i = -1; i <= 1; i++) {
            for (let j = -1; j <= 1; j++) {
                const neighborX = cellX + i;
                const neighborY = cellY + j;
                
                // Generate random point in cell
                const random = this.seededRandom(neighborX * 374761393 + neighborY * 668265263);
                const pointX = neighborX + random();
                const pointY = neighborY + random();
                
                // Calculate distance
                const dx = (x * scale) - pointX;
                const dy = (y * scale) - pointY;
                const dist = Math.sqrt(dx * dx + dy * dy);
                
                minDist = Math.min(minDist, dist);
            }
        }
        
        return minDist;
    }
    
    // Simplex noise (2D) - alternative to Perlin with better characteristics
    simplex(x, y) {
        const F2 = 0.5 * (Math.sqrt(3.0) - 1.0);
        const G2 = (3.0 - Math.sqrt(3.0)) / 6.0;
        
        // Skew the input space
        const s = (x + y) * F2;
        const i = Math.floor(x + s);
        const j = Math.floor(y + s);
        
        const t = (i + j) * G2;
        const X0 = i - t;
        const Y0 = j - t;
        const x0 = x - X0;
        const y0 = y - Y0;
        
        // Determine which simplex we are in
        let i1, j1;
        if (x0 > y0) {
            i1 = 1; j1 = 0;
        } else {
            i1 = 0; j1 = 1;
        }
        
        // Offsets for middle and last corners
        const x1 = x0 - i1 + G2;
        const y1 = y0 - j1 + G2;
        const x2 = x0 - 1.0 + 2.0 * G2;
        const y2 = y0 - 1.0 + 2.0 * G2;
        
        // Work out the hashed gradient indices
        const ii = i & 255;
        const jj = j & 255;
        const gi0 = this.permutation[ii + this.permutation[jj]] % 12;
        const gi1 = this.permutation[ii + i1 + this.permutation[jj + j1]] % 12;
        const gi2 = this.permutation[ii + 1 + this.permutation[jj + 1]] % 12;
        
        // Calculate contributions from the three corners
        let n0, n1, n2;
        
        let t0 = 0.5 - x0 * x0 - y0 * y0;
        if (t0 < 0) {
            n0 = 0.0;
        } else {
            t0 *= t0;
            n0 = t0 * t0 * this.grad(gi0, x0, y0);
        }
        
        let t1 = 0.5 - x1 * x1 - y1 * y1;
        if (t1 < 0) {
            n1 = 0.0;
        } else {
            t1 *= t1;
            n1 = t1 * t1 * this.grad(gi1, x1, y1);
        }
        
        let t2 = 0.5 - x2 * x2 - y2 * y2;
        if (t2 < 0) {
            n2 = 0.0;
        } else {
            t2 *= t2;
            n2 = t2 * t2 * this.grad(gi2, x2, y2);
        }
        
        // Add contributions and scale to [-1, 1]
        return 70.0 * (n0 + n1 + n2);
    }
}
