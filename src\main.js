import * as THREE from 'three';
import { GUI } from 'dat.gui';
import { TerrainGenerator } from './terrain/TerrainGenerator.js';
import { NoiseGenerator } from './noise/NoiseGenerator.js';
import { ErosionSimulator } from './erosion/ErosionSimulator.js';
import { RiverGenerator } from './rivers/RiverGenerator.js';
import { CameraController } from './controls/CameraController.js';
import { ExportUtils } from './utils/ExportUtils.js';

class TerrainSynthesis {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.terrainGenerator = null;
        this.cameraController = null;
        this.gui = null;
        
        // Terrain parameters
        this.params = {
            // Terrain size
            width: 512,
            height: 512,
            scale: 100,
            
            // Noise parameters
            noiseScale: 0.01,
            octaves: 6,
            persistence: 0.5,
            lacunarity: 2.0,
            amplitude: 50,
            
            // Erosion parameters
            erosionIterations: 100,
            erosionStrength: 0.1,
            evaporationRate: 0.01,
            
            // River parameters
            riverThreshold: 0.1,
            riverWidth: 2,
            riverDepth: 5,
            
            // Visualization
            wireframe: false,
            showRivers: true,
            colorByHeight: true,
            
            // Actions
            generateTerrain: () => this.generateTerrain(),
            exportHeightmapPNG: () => this.exportHeightmapPNG(),
            exportTerrainOBJ: () => this.exportTerrainOBJ(),
            exportParameters: () => this.exportParameters(),
            randomSeed: () => this.randomizeSeed()
        };
        
        this.init();
    }
    
    init() {
        this.setupScene();
        this.setupRenderer();
        this.setupCamera();
        this.setupLighting();
        this.setupControls();
        this.setupGUI();
        
        // Generate initial terrain
        this.generateTerrain();
        
        // Start render loop
        this.animate();
        
        // Hide loading screen
        document.getElementById('loading').style.display = 'none';
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        this.scene.fog = new THREE.Fog(0x87CEEB, 100, 1000);
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(this.renderer.domElement);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            2000
        );
        this.camera.position.set(0, 100, 100);
    }
    
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -200;
        directionalLight.shadow.camera.right = 200;
        directionalLight.shadow.camera.top = 200;
        directionalLight.shadow.camera.bottom = -200;
        this.scene.add(directionalLight);
    }
    
    setupControls() {
        this.cameraController = new CameraController(this.camera, this.renderer.domElement);
    }
    
    setupGUI() {
        this.gui = new GUI({ autoPlace: false });
        document.getElementById('controls').appendChild(this.gui.domElement);
        
        // Terrain folder
        const terrainFolder = this.gui.addFolder('Terrain');
        terrainFolder.add(this.params, 'width', 64, 1024, 64);
        terrainFolder.add(this.params, 'height', 64, 1024, 64);
        terrainFolder.add(this.params, 'scale', 10, 200, 10);
        
        // Noise folder
        const noiseFolder = this.gui.addFolder('Noise');
        noiseFolder.add(this.params, 'noiseScale', 0.001, 0.1, 0.001);
        noiseFolder.add(this.params, 'octaves', 1, 10, 1);
        noiseFolder.add(this.params, 'persistence', 0.1, 1.0, 0.1);
        noiseFolder.add(this.params, 'lacunarity', 1.0, 4.0, 0.1);
        noiseFolder.add(this.params, 'amplitude', 10, 200, 5);
        
        // Erosion folder
        const erosionFolder = this.gui.addFolder('Erosion');
        erosionFolder.add(this.params, 'erosionIterations', 0, 500, 10);
        erosionFolder.add(this.params, 'erosionStrength', 0.01, 1.0, 0.01);
        erosionFolder.add(this.params, 'evaporationRate', 0.001, 0.1, 0.001);
        
        // Rivers folder
        const riversFolder = this.gui.addFolder('Rivers');
        riversFolder.add(this.params, 'riverThreshold', 0.01, 1.0, 0.01);
        riversFolder.add(this.params, 'riverWidth', 1, 10, 1);
        riversFolder.add(this.params, 'riverDepth', 1, 20, 1);
        
        // Visualization folder
        const vizFolder = this.gui.addFolder('Visualization');
        vizFolder.add(this.params, 'wireframe').onChange(() => this.updateVisualization());
        vizFolder.add(this.params, 'showRivers').onChange(() => this.updateVisualization());
        vizFolder.add(this.params, 'colorByHeight').onChange(() => this.updateVisualization());
        
        // Actions
        const actionsFolder = this.gui.addFolder('Actions');
        actionsFolder.add(this.params, 'generateTerrain');
        actionsFolder.add(this.params, 'randomSeed');
        actionsFolder.add(this.params, 'exportHeightmapPNG');
        actionsFolder.add(this.params, 'exportTerrainOBJ');
        actionsFolder.add(this.params, 'exportParameters');
        actionsFolder.open();
        
        // Open folders by default
        terrainFolder.open();
        noiseFolder.open();
    }
    
    generateTerrain() {
        console.log('Generating terrain...');
        
        // Initialize generators if not already done
        if (!this.terrainGenerator) {
            this.terrainGenerator = new TerrainGenerator();
        }
        
        // Generate terrain with current parameters
        const terrain = this.terrainGenerator.generate(this.params);
        
        // Remove existing terrain from scene
        const existingTerrain = this.scene.getObjectByName('terrain');
        if (existingTerrain) {
            this.scene.remove(existingTerrain);
        }
        
        // Add new terrain to scene
        terrain.name = 'terrain';
        this.scene.add(terrain);
        
        console.log('Terrain generation complete');
    }
    
    updateVisualization() {
        const terrain = this.scene.getObjectByName('terrain');
        if (terrain) {
            terrain.material.wireframe = this.params.wireframe;
            // Additional visualization updates will be implemented
        }
    }
    
    exportHeightmapPNG() {
        if (this.terrainGenerator && this.terrainGenerator.heightData) {
            ExportUtils.exportHeightmapPNG(this.terrainGenerator.heightData);
        } else {
            alert('Please generate terrain first!');
        }
    }

    exportTerrainOBJ() {
        if (this.terrainGenerator && this.terrainGenerator.heightData) {
            ExportUtils.exportTerrainOBJ(this.terrainGenerator.heightData, this.params.scale / 10);
        } else {
            alert('Please generate terrain first!');
        }
    }

    exportParameters() {
        ExportUtils.exportParameters(this.params);
    }

    randomizeSeed() {
        // Randomize noise parameters for different terrain
        this.params.noiseScale = 0.005 + Math.random() * 0.02;
        this.params.octaves = 4 + Math.floor(Math.random() * 4);
        this.params.persistence = 0.3 + Math.random() * 0.4;
        this.params.lacunarity = 1.5 + Math.random() * 1.0;
        this.params.amplitude = 30 + Math.random() * 40;

        // Update GUI display
        this.gui.updateDisplay();

        // Generate new terrain
        this.generateTerrain();
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.cameraController) {
            this.cameraController.update();
        }
        
        this.renderer.render(this.scene, this.camera);
    }
}

// Start the application
new TerrainSynthesis();
