<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terrain Synthesis - 3D Procedural Terrain Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="info">
        <h3>Terrain Synthesis</h3>
        <p>WASD: Move camera | Mouse: Look around | Scroll: Zoom</p>
        <p>Use controls panel to adjust terrain parameters</p>
    </div>
    <div id="controls"></div>
    <div id="loading">Loading terrain generator...</div>
    
    <script type="module" src="src/main.js"></script>
</body>
</html>
