<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terrain Synthesis - 3D Procedural Terrain Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }

        #container {
            width: 100vw;
            height: 100vh;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }

        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }

        #error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: red;
            font-size: 16px;
            z-index: 200;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="info">
        <h3>Terrain Synthesis</h3>
        <p>WASD: Move camera | Mouse: Look around | Scroll: Zoom</p>
        <p>Use controls panel to adjust terrain parameters</p>
    </div>
    <div id="controls"></div>
    <div id="loading">Loading terrain generator...</div>
    <div id="error">
        <h3>Error Loading Application</h3>
        <p id="error-message"></p>
        <p>Please check the browser console for more details.</p>
    </div>

    <!-- Load Three.js and dat.GUI from CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.9/dat.gui.min.js"></script>

    <script type="module">
        // Error handling
        window.addEventListener('error', function(e) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = e.message;
            console.error('Application error:', e);
        });

        // Check if Three.js loaded
        if (typeof THREE === 'undefined') {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = 'Failed to load Three.js library';
        } else {
            // Load the main application
            import('./src/main-standalone.js').catch(err => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error-message').textContent = 'Failed to load application: ' + err.message;
                console.error('Module loading error:', err);
            });
        }
    </script>
</body>
</html>
