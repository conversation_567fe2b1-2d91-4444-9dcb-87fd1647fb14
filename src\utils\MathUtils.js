export class MathUtils {
    // Clamp value between min and max
    static clamp(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }
    
    // Linear interpolation
    static lerp(a, b, t) {
        return a + (b - a) * t;
    }
    
    // Smooth step function
    static smoothstep(edge0, edge1, x) {
        const t = this.clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0);
        return t * t * (3.0 - 2.0 * t);
    }
    
    // Smoother step function
    static smootherstep(edge0, edge1, x) {
        const t = this.clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0);
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    // Map value from one range to another
    static map(value, inMin, inMax, outMin, outMax) {
        return outMin + (outMax - outMin) * ((value - inMin) / (inMax - inMin));
    }
    
    // Distance between two points
    static distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    // Normalize angle to [-PI, PI]
    static normalizeAngle(angle) {
        while (angle > Math.PI) angle -= 2 * Math.PI;
        while (angle < -Math.PI) angle += 2 * Math.PI;
        return angle;
    }
    
    // Convert degrees to radians
    static degToRad(degrees) {
        return degrees * Math.PI / 180;
    }
    
    // Convert radians to degrees
    static radToDeg(radians) {
        return radians * 180 / Math.PI;
    }
    
    // Generate random number with seed
    static seededRandom(seed) {
        const x = Math.sin(seed) * 10000;
        return x - Math.floor(x);
    }
    
    // Gaussian random number generator (Box-Muller transform)
    static gaussianRandom(mean = 0, stdDev = 1) {
        let u = 0, v = 0;
        while (u === 0) u = Math.random(); // Converting [0,1) to (0,1)
        while (v === 0) v = Math.random();
        
        const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        return z * stdDev + mean;
    }
    
    // Bilinear interpolation
    static bilinearInterpolation(x, y, x1, y1, x2, y2, q11, q12, q21, q22) {
        const r1 = ((x2 - x) / (x2 - x1)) * q11 + ((x - x1) / (x2 - x1)) * q21;
        const r2 = ((x2 - x) / (x2 - x1)) * q12 + ((x - x1) / (x2 - x1)) * q22;
        return ((y2 - y) / (y2 - y1)) * r1 + ((y - y1) / (y2 - y1)) * r2;
    }
    
    // Cubic interpolation
    static cubicInterpolation(p0, p1, p2, p3, t) {
        const a = -0.5 * p0 + 1.5 * p1 - 1.5 * p2 + 0.5 * p3;
        const b = p0 - 2.5 * p1 + 2 * p2 - 0.5 * p3;
        const c = -0.5 * p0 + 0.5 * p2;
        const d = p1;
        
        return a * t * t * t + b * t * t + c * t + d;
    }
    
    // Hermite interpolation
    static hermiteInterpolation(p0, p1, m0, m1, t) {
        const t2 = t * t;
        const t3 = t2 * t;
        
        const h00 = 2 * t3 - 3 * t2 + 1;
        const h10 = t3 - 2 * t2 + t;
        const h01 = -2 * t3 + 3 * t2;
        const h11 = t3 - t2;
        
        return h00 * p0 + h10 * m0 + h01 * p1 + h11 * m1;
    }
    
    // Fractal sum (for terrain generation)
    static fractalSum(x, y, noiseFunction, octaves, persistence, lacunarity, scale) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += noiseFunction(x * frequency, y * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return value / maxValue;
    }
    
    // Turbulence (absolute value of fractal sum)
    static turbulence(x, y, noiseFunction, octaves, persistence, lacunarity, scale) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            value += Math.abs(noiseFunction(x * frequency, y * frequency)) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return value / maxValue;
    }
    
    // Ridge function (for mountain ridges)
    static ridge(x, y, noiseFunction, octaves, persistence, lacunarity, scale) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            let n = Math.abs(noiseFunction(x * frequency, y * frequency));
            n = 1 - n;
            n = n * n;
            value += n * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return value / maxValue;
    }
    
    // Warping function for domain distortion
    static warp(x, y, noiseFunction, strength) {
        const warpX = noiseFunction(x * 0.1, y * 0.1) * strength;
        const warpY = noiseFunction((x + 100) * 0.1, (y + 100) * 0.1) * strength;
        
        return {
            x: x + warpX,
            y: y + warpY
        };
    }
    
    // Cellular automata step
    static cellularAutomata(grid, width, height, birthLimit, deathLimit) {
        const newGrid = new Array(width * height);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                const neighbors = this.countNeighbors(grid, x, y, width, height);
                
                if (grid[index] === 1) {
                    newGrid[index] = neighbors >= deathLimit ? 1 : 0;
                } else {
                    newGrid[index] = neighbors > birthLimit ? 1 : 0;
                }
            }
        }
        
        return newGrid;
    }
    
    // Count neighbors for cellular automata
    static countNeighbors(grid, x, y, width, height) {
        let count = 0;
        
        for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
                if (dx === 0 && dy === 0) continue;
                
                const nx = x + dx;
                const ny = y + dy;
                
                if (nx < 0 || nx >= width || ny < 0 || ny >= height) {
                    count++; // Treat out-of-bounds as solid
                } else {
                    const index = ny * width + nx;
                    count += grid[index];
                }
            }
        }
        
        return count;
    }
    
    // Generate Poisson disk samples
    static poissonDiskSampling(width, height, radius, maxAttempts = 30) {
        const cellSize = radius / Math.sqrt(2);
        const gridWidth = Math.ceil(width / cellSize);
        const gridHeight = Math.ceil(height / cellSize);
        const grid = new Array(gridWidth * gridHeight).fill(-1);
        const points = [];
        const activeList = [];
        
        // Add initial point
        const initialX = Math.random() * width;
        const initialY = Math.random() * height;
        const initialPoint = { x: initialX, y: initialY };
        points.push(initialPoint);
        activeList.push(0);
        
        const gridX = Math.floor(initialX / cellSize);
        const gridY = Math.floor(initialY / cellSize);
        grid[gridY * gridWidth + gridX] = 0;
        
        while (activeList.length > 0) {
            const randomIndex = Math.floor(Math.random() * activeList.length);
            const pointIndex = activeList[randomIndex];
            const point = points[pointIndex];
            let found = false;
            
            for (let attempt = 0; attempt < maxAttempts; attempt++) {
                const angle = Math.random() * 2 * Math.PI;
                const distance = radius + Math.random() * radius;
                const newX = point.x + Math.cos(angle) * distance;
                const newY = point.y + Math.sin(angle) * distance;
                
                if (newX >= 0 && newX < width && newY >= 0 && newY < height) {
                    const newGridX = Math.floor(newX / cellSize);
                    const newGridY = Math.floor(newY / cellSize);
                    
                    let valid = true;
                    
                    // Check surrounding cells
                    for (let dy = -2; dy <= 2 && valid; dy++) {
                        for (let dx = -2; dx <= 2 && valid; dx++) {
                            const checkX = newGridX + dx;
                            const checkY = newGridY + dy;
                            
                            if (checkX >= 0 && checkX < gridWidth && checkY >= 0 && checkY < gridHeight) {
                                const checkIndex = grid[checkY * gridWidth + checkX];
                                if (checkIndex !== -1) {
                                    const checkPoint = points[checkIndex];
                                    const dist = this.distance(newX, newY, checkPoint.x, checkPoint.y);
                                    if (dist < radius) {
                                        valid = false;
                                    }
                                }
                            }
                        }
                    }
                    
                    if (valid) {
                        const newPoint = { x: newX, y: newY };
                        points.push(newPoint);
                        activeList.push(points.length - 1);
                        grid[newGridY * gridWidth + newGridX] = points.length - 1;
                        found = true;
                        break;
                    }
                }
            }
            
            if (!found) {
                activeList.splice(randomIndex, 1);
            }
        }
        
        return points;
    }
}
