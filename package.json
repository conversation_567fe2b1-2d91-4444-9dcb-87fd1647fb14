{"name": "terrain-synthesis", "version": "1.0.0", "description": "Automated terrain synthesis model for generating realistic 3D terrain with rivers, valleys, and mountains", "main": "src/main.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "python -m http.server 8000"}, "keywords": ["terrain", "3d", "procedural", "generation", "graphics"], "author": "", "license": "MIT", "dependencies": {"three": "^0.158.0", "dat.gui": "^0.7.9"}, "devDependencies": {"vite": "^5.0.0"}}