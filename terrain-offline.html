<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terrain Synthesis - Offline 3D Terrain Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #canvas {
            display: block;
            cursor: crosshair;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            min-width: 200px;
            z-index: 100;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }
        
        button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .value {
            font-size: 11px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="info">
            <h3>🏔️ Terrain Generator</h3>
            <p><strong>Controls:</strong></p>
            <p>• Mouse: Rotate view</p>
            <p>• Scroll: Zoom in/out</p>
            <p>• Use panel to adjust terrain</p>
        </div>
        
        <div id="controls">
            <h4>Terrain Settings</h4>
            
            <div class="control-group">
                <label>Terrain Size: <span class="value" id="sizeValue">64</span></label>
                <input type="range" id="size" min="32" max="128" value="64" step="16">
            </div>
            
            <div class="control-group">
                <label>Noise Scale: <span class="value" id="scaleValue">0.05</span></label>
                <input type="range" id="scale" min="0.01" max="0.2" value="0.05" step="0.01">
            </div>
            
            <div class="control-group">
                <label>Octaves: <span class="value" id="octavesValue">4</span></label>
                <input type="range" id="octaves" min="1" max="8" value="4" step="1">
            </div>
            
            <div class="control-group">
                <label>Amplitude: <span class="value" id="amplitudeValue">50</span></label>
                <input type="range" id="amplitude" min="10" max="100" value="50" step="5">
            </div>
            
            <div class="control-group">
                <label>Persistence: <span class="value" id="persistenceValue">0.5</span></label>
                <input type="range" id="persistence" min="0.1" max="1.0" value="0.5" step="0.1">
            </div>
            
            <button onclick="generateTerrain()">🔄 Generate New Terrain</button>
            <button onclick="randomizeTerrain()">🎲 Random Terrain</button>
            <button onclick="exportHeightmap()">💾 Export Heightmap</button>
            
            <div style="margin-top: 10px; font-size: 11px; color: #ccc;">
                <p>Drag to rotate • Scroll to zoom</p>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let canvas, ctx;
        let terrainData = [];
        let rotationX = -0.5, rotationY = 0;
        let zoom = 1;
        let isDragging = false;
        let lastMouseX = 0, lastMouseY = 0;
        
        // Terrain parameters
        let params = {
            size: 64,
            scale: 0.05,
            octaves: 4,
            amplitude: 50,
            persistence: 0.5
        };
        
        // Simple Perlin noise implementation
        class PerlinNoise {
            constructor() {
                this.permutation = [];
                for (let i = 0; i < 256; i++) {
                    this.permutation[i] = Math.floor(Math.random() * 256);
                }
                for (let i = 0; i < 256; i++) {
                    this.permutation[256 + i] = this.permutation[i];
                }
            }
            
            fade(t) {
                return t * t * t * (t * (t * 6 - 15) + 10);
            }
            
            lerp(a, b, t) {
                return a + t * (b - a);
            }
            
            grad(hash, x, y) {
                const h = hash & 15;
                const u = h < 8 ? x : y;
                const v = h < 4 ? y : h === 12 || h === 14 ? x : 0;
                return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
            }
            
            noise(x, y) {
                const X = Math.floor(x) & 255;
                const Y = Math.floor(y) & 255;
                x -= Math.floor(x);
                y -= Math.floor(y);
                const u = this.fade(x);
                const v = this.fade(y);
                const A = this.permutation[X] + Y;
                const B = this.permutation[X + 1] + Y;
                
                return this.lerp(
                    this.lerp(this.grad(this.permutation[A], x, y),
                             this.grad(this.permutation[B], x - 1, y), u),
                    this.lerp(this.grad(this.permutation[A + 1], x, y - 1),
                             this.grad(this.permutation[B + 1], x - 1, y - 1), u), v
                );
            }
        }
        
        const noise = new PerlinNoise();
        
        // Initialize the application
        function init() {
            canvas = document.getElementById('canvas');
            ctx = canvas.getContext('2d');
            
            // Set canvas size
            resizeCanvas();
            
            // Setup event listeners
            setupControls();
            setupMouseControls();
            
            // Generate initial terrain
            generateTerrain();
            
            // Start render loop
            render();
            
            console.log('Terrain generator initialized!');
        }
        
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        function setupControls() {
            // Update parameter values when sliders change
            const controls = ['size', 'scale', 'octaves', 'amplitude', 'persistence'];
            
            controls.forEach(control => {
                const slider = document.getElementById(control);
                const valueSpan = document.getElementById(control + 'Value');
                
                slider.addEventListener('input', () => {
                    params[control] = parseFloat(slider.value);
                    valueSpan.textContent = slider.value;
                    generateTerrain();
                });
            });
            
            // Window resize
            window.addEventListener('resize', resizeCanvas);
        }
        
        function setupMouseControls() {
            canvas.addEventListener('mousedown', (e) => {
                isDragging = true;
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            });
            
            canvas.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const deltaX = e.clientX - lastMouseX;
                    const deltaY = e.clientY - lastMouseY;
                    
                    rotationY += deltaX * 0.01;
                    rotationX += deltaY * 0.01;
                    
                    // Clamp rotation
                    rotationX = Math.max(-Math.PI/2, Math.min(Math.PI/2, rotationX));
                    
                    lastMouseX = e.clientX;
                    lastMouseY = e.clientY;
                }
            });
            
            canvas.addEventListener('mouseup', () => {
                isDragging = false;
            });
            
            canvas.addEventListener('wheel', (e) => {
                zoom *= (e.deltaY > 0) ? 0.9 : 1.1;
                zoom = Math.max(0.1, Math.min(5, zoom));
                e.preventDefault();
            });
        }
        
        function generateTerrain() {
            const { size, scale, octaves, amplitude, persistence } = params;
            terrainData = [];
            
            console.log('Generating terrain with size:', size);
            
            for (let y = 0; y < size; y++) {
                terrainData[y] = [];
                for (let x = 0; x < size; x++) {
                    let height = 0;
                    let freq = scale;
                    let amp = amplitude;
                    let maxValue = 0;
                    
                    // Generate multi-octave noise
                    for (let i = 0; i < octaves; i++) {
                        height += noise.noise(x * freq, y * freq) * amp;
                        maxValue += amp;
                        amp *= persistence;
                        freq *= 2;
                    }
                    
                    terrainData[y][x] = height / maxValue;
                }
            }
        }
        
        function getHeightColor(height) {
            const normalized = (height + params.amplitude) / (params.amplitude * 2);
            
            if (normalized < 0.3) {
                return `rgb(${Math.floor(30 + normalized * 100)}, ${Math.floor(100 + normalized * 100)}, 200)`; // Blue water
            } else if (normalized < 0.5) {
                return `rgb(200, ${Math.floor(180 + normalized * 50)}, 100)`; // Sandy beach
            } else if (normalized < 0.7) {
                return `rgb(${Math.floor(50 + normalized * 100)}, ${Math.floor(150 + normalized * 50)}, 50)`; // Green grass
            } else {
                return `rgb(${Math.floor(200 + normalized * 55)}, ${Math.floor(200 + normalized * 55)}, ${Math.floor(200 + normalized * 55)})`; // White snow
            }
        }
        
        function render() {
            // Clear canvas
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const size = params.size;
            
            // Simple 3D projection
            const points = [];
            
            for (let y = 0; y < size - 1; y++) {
                for (let x = 0; x < size - 1; x++) {
                    const x1 = (x - size/2) * 4 * zoom;
                    const y1 = (y - size/2) * 4 * zoom;
                    const z1 = terrainData[y][x] * zoom;
                    
                    const x2 = ((x+1) - size/2) * 4 * zoom;
                    const y2 = (y - size/2) * 4 * zoom;
                    const z2 = terrainData[y][x+1] * zoom;
                    
                    const x3 = (x - size/2) * 4 * zoom;
                    const y3 = ((y+1) - size/2) * 4 * zoom;
                    const z3 = terrainData[y+1][x] * zoom;
                    
                    // Rotate points
                    const p1 = rotatePoint(x1, y1, z1);
                    const p2 = rotatePoint(x2, y2, z2);
                    const p3 = rotatePoint(x3, y3, z3);
                    
                    // Project to 2D
                    const proj1 = project3D(p1.x, p1.y, p1.z);
                    const proj2 = project3D(p2.x, p2.y, p2.z);
                    const proj3 = project3D(p3.x, p3.y, p3.z);
                    
                    // Draw triangle
                    ctx.fillStyle = getHeightColor(terrainData[y][x]);
                    ctx.beginPath();
                    ctx.moveTo(centerX + proj1.x, centerY + proj1.y);
                    ctx.lineTo(centerX + proj2.x, centerY + proj2.y);
                    ctx.lineTo(centerX + proj3.x, centerY + proj3.y);
                    ctx.closePath();
                    ctx.fill();
                    
                    // Optional: draw wireframe
                    ctx.strokeStyle = 'rgba(0,0,0,0.1)';
                    ctx.lineWidth = 0.5;
                    ctx.stroke();
                }
            }
            
            requestAnimationFrame(render);
        }
        
        function rotatePoint(x, y, z) {
            // Rotate around X axis
            const y1 = y * Math.cos(rotationX) - z * Math.sin(rotationX);
            const z1 = y * Math.sin(rotationX) + z * Math.cos(rotationX);
            
            // Rotate around Y axis
            const x2 = x * Math.cos(rotationY) + z1 * Math.sin(rotationY);
            const z2 = -x * Math.sin(rotationY) + z1 * Math.cos(rotationY);
            
            return { x: x2, y: y1, z: z2 };
        }
        
        function project3D(x, y, z) {
            const distance = 300;
            const scale = distance / (distance + z);
            return {
                x: x * scale,
                y: y * scale
            };
        }
        
        function randomizeTerrain() {
            // Randomize parameters
            document.getElementById('scale').value = (0.02 + Math.random() * 0.1).toFixed(2);
            document.getElementById('octaves').value = Math.floor(3 + Math.random() * 5);
            document.getElementById('amplitude').value = Math.floor(30 + Math.random() * 60);
            document.getElementById('persistence').value = (0.3 + Math.random() * 0.5).toFixed(1);
            
            // Update params and regenerate
            ['scale', 'octaves', 'amplitude', 'persistence'].forEach(control => {
                const slider = document.getElementById(control);
                const valueSpan = document.getElementById(control + 'Value');
                params[control] = parseFloat(slider.value);
                valueSpan.textContent = slider.value;
            });
            
            generateTerrain();
        }
        
        function exportHeightmap() {
            const size = params.size;
            const exportCanvas = document.createElement('canvas');
            exportCanvas.width = size;
            exportCanvas.height = size;
            const exportCtx = exportCanvas.getContext('2d');
            
            const imageData = exportCtx.createImageData(size, size);
            
            for (let y = 0; y < size; y++) {
                for (let x = 0; x < size; x++) {
                    const height = terrainData[y][x];
                    const normalized = Math.floor((height + params.amplitude) / (params.amplitude * 2) * 255);
                    const index = (y * size + x) * 4;
                    
                    imageData.data[index] = normalized;     // R
                    imageData.data[index + 1] = normalized; // G
                    imageData.data[index + 2] = normalized; // B
                    imageData.data[index + 3] = 255;        // A
                }
            }
            
            exportCtx.putImageData(imageData, 0, 0);
            
            // Download the image
            const link = document.createElement('a');
            link.download = 'heightmap.png';
            link.href = exportCanvas.toDataURL();
            link.click();
        }
        
        // Start the application
        window.addEventListener('load', init);
    </script>
</body>
</html>
