export class ExportUtils {
    // Export heightmap as PNG image
    static exportHeightmapPNG(heightData, filename = 'heightmap.png') {
        const { width, height, data } = heightData;
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext('2d');
        const imageData = context.createImageData(width, height);
        
        // Find min and max values for normalization
        let min = Infinity;
        let max = -Infinity;
        for (let i = 0; i < data.length; i++) {
            min = Math.min(min, data[i]);
            max = Math.max(max, data[i]);
        }
        
        const range = max - min;
        
        // Convert height data to grayscale image
        for (let i = 0; i < data.length; i++) {
            const normalizedValue = range > 0 ? (data[i] - min) / range : 0;
            const grayValue = Math.floor(normalizedValue * 255);
            const pixelIndex = i * 4;
            
            imageData.data[pixelIndex] = grayValue;     // R
            imageData.data[pixelIndex + 1] = grayValue; // G
            imageData.data[pixelIndex + 2] = grayValue; // B
            imageData.data[pixelIndex + 3] = 255;       // A
        }
        
        context.putImageData(imageData, 0, 0);
        
        // Download the image
        const link = document.createElement('a');
        link.download = filename;
        link.href = canvas.toDataURL('image/png');
        link.click();
        
        return canvas.toDataURL('image/png');
    }
    
    // Export heightmap as raw binary data
    static exportHeightmapRAW(heightData, filename = 'heightmap.raw') {
        const { data } = heightData;
        const buffer = new ArrayBuffer(data.length * 4); // 4 bytes per float
        const view = new Float32Array(buffer);
        
        for (let i = 0; i < data.length; i++) {
            view[i] = data[i];
        }
        
        const blob = new Blob([buffer], { type: 'application/octet-stream' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        
        return blob;
    }
    
    // Export terrain as OBJ file
    static exportTerrainOBJ(heightData, scale = 1, filename = 'terrain.obj') {
        const { width, height, data } = heightData;
        let objContent = '# Terrain OBJ file\n';
        objContent += `# Generated terrain ${width}x${height}\n\n`;
        
        // Write vertices
        objContent += '# Vertices\n';
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                const worldX = (x - width / 2) * scale;
                const worldY = data[index] * scale;
                const worldZ = (y - height / 2) * scale;
                objContent += `v ${worldX.toFixed(6)} ${worldY.toFixed(6)} ${worldZ.toFixed(6)}\n`;
            }
        }
        
        // Write texture coordinates
        objContent += '\n# Texture coordinates\n';
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const u = x / (width - 1);
                const v = y / (height - 1);
                objContent += `vt ${u.toFixed(6)} ${v.toFixed(6)}\n`;
            }
        }
        
        // Write normals (simplified - pointing up)
        objContent += '\n# Normals\n';
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                // Calculate normal based on neighboring heights
                let nx = 0, ny = 1, nz = 0;
                
                if (x > 0 && x < width - 1 && y > 0 && y < height - 1) {
                    const hL = data[y * width + (x - 1)];
                    const hR = data[y * width + (x + 1)];
                    const hD = data[(y - 1) * width + x];
                    const hU = data[(y + 1) * width + x];
                    
                    nx = (hL - hR) / (2 * scale);
                    nz = (hD - hU) / (2 * scale);
                    ny = 1;
                    
                    // Normalize
                    const length = Math.sqrt(nx * nx + ny * ny + nz * nz);
                    nx /= length;
                    ny /= length;
                    nz /= length;
                }
                
                objContent += `vn ${nx.toFixed(6)} ${ny.toFixed(6)} ${nz.toFixed(6)}\n`;
            }
        }
        
        // Write faces
        objContent += '\n# Faces\n';
        for (let y = 0; y < height - 1; y++) {
            for (let x = 0; x < width - 1; x++) {
                const i1 = y * width + x + 1;       // 1-based indexing for OBJ
                const i2 = (y + 1) * width + x + 1;
                const i3 = (y + 1) * width + (x + 1) + 1;
                const i4 = y * width + (x + 1) + 1;
                
                // Two triangles per quad
                objContent += `f ${i1}/${i1}/${i1} ${i2}/${i2}/${i2} ${i3}/${i3}/${i3}\n`;
                objContent += `f ${i1}/${i1}/${i1} ${i3}/${i3}/${i3} ${i4}/${i4}/${i4}\n`;
            }
        }
        
        // Download the file
        const blob = new Blob([objContent], { type: 'text/plain' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        
        return objContent;
    }
    
    // Export terrain parameters as JSON
    static exportParameters(params, filename = 'terrain_params.json') {
        const jsonContent = JSON.stringify(params, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        
        return jsonContent;
    }
    
    // Import terrain parameters from JSON
    static importParameters(file, callback) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const params = JSON.parse(e.target.result);
                callback(params);
            } catch (error) {
                console.error('Error parsing parameter file:', error);
                alert('Error loading parameter file. Please check the file format.');
            }
        };
        reader.readAsText(file);
    }
    
    // Export river network as SVG
    static exportRiverNetworkSVG(riverData, width, height, filename = 'rivers.svg') {
        if (!riverData || !riverData.riverNetwork) return;
        
        const { riverNetwork } = riverData;
        let svgContent = `<?xml version="1.0" encoding="UTF-8"?>\n`;
        svgContent += `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">\n`;
        svgContent += `<rect width="100%" height="100%" fill="white"/>\n`;
        
        // Draw river network
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                if (riverNetwork[index] === 1) {
                    svgContent += `<rect x="${x}" y="${y}" width="1" height="1" fill="blue"/>\n`;
                }
            }
        }
        
        svgContent += `</svg>`;
        
        // Download the file
        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        
        return svgContent;
    }
    
    // Export terrain as STL file for 3D printing
    static exportTerrainSTL(heightData, scale = 1, filename = 'terrain.stl') {
        const { width, height, data } = heightData;
        let stlContent = 'solid terrain\n';
        
        // Generate triangles for the terrain surface
        for (let y = 0; y < height - 1; y++) {
            for (let x = 0; x < width - 1; x++) {
                const i1 = y * width + x;
                const i2 = (y + 1) * width + x;
                const i3 = (y + 1) * width + (x + 1);
                const i4 = y * width + (x + 1);
                
                const v1 = {
                    x: (x - width / 2) * scale,
                    y: data[i1] * scale,
                    z: (y - height / 2) * scale
                };
                const v2 = {
                    x: (x - width / 2) * scale,
                    y: data[i2] * scale,
                    z: ((y + 1) - height / 2) * scale
                };
                const v3 = {
                    x: ((x + 1) - width / 2) * scale,
                    y: data[i3] * scale,
                    z: ((y + 1) - height / 2) * scale
                };
                const v4 = {
                    x: ((x + 1) - width / 2) * scale,
                    y: data[i4] * scale,
                    z: (y - height / 2) * scale
                };
                
                // First triangle
                const n1 = this.calculateNormal(v1, v2, v3);
                stlContent += `facet normal ${n1.x.toFixed(6)} ${n1.y.toFixed(6)} ${n1.z.toFixed(6)}\n`;
                stlContent += `  outer loop\n`;
                stlContent += `    vertex ${v1.x.toFixed(6)} ${v1.y.toFixed(6)} ${v1.z.toFixed(6)}\n`;
                stlContent += `    vertex ${v2.x.toFixed(6)} ${v2.y.toFixed(6)} ${v2.z.toFixed(6)}\n`;
                stlContent += `    vertex ${v3.x.toFixed(6)} ${v3.y.toFixed(6)} ${v3.z.toFixed(6)}\n`;
                stlContent += `  endloop\n`;
                stlContent += `endfacet\n`;
                
                // Second triangle
                const n2 = this.calculateNormal(v1, v3, v4);
                stlContent += `facet normal ${n2.x.toFixed(6)} ${n2.y.toFixed(6)} ${n2.z.toFixed(6)}\n`;
                stlContent += `  outer loop\n`;
                stlContent += `    vertex ${v1.x.toFixed(6)} ${v1.y.toFixed(6)} ${v1.z.toFixed(6)}\n`;
                stlContent += `    vertex ${v3.x.toFixed(6)} ${v3.y.toFixed(6)} ${v3.z.toFixed(6)}\n`;
                stlContent += `    vertex ${v4.x.toFixed(6)} ${v4.y.toFixed(6)} ${v4.z.toFixed(6)}\n`;
                stlContent += `  endloop\n`;
                stlContent += `endfacet\n`;
            }
        }
        
        stlContent += 'endsolid terrain\n';
        
        // Download the file
        const blob = new Blob([stlContent], { type: 'text/plain' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        
        return stlContent;
    }
    
    // Calculate normal vector for STL export
    static calculateNormal(v1, v2, v3) {
        const u = {
            x: v2.x - v1.x,
            y: v2.y - v1.y,
            z: v2.z - v1.z
        };
        const v = {
            x: v3.x - v1.x,
            y: v3.y - v1.y,
            z: v3.z - v1.z
        };
        
        const normal = {
            x: u.y * v.z - u.z * v.y,
            y: u.z * v.x - u.x * v.z,
            z: u.x * v.y - u.y * v.x
        };
        
        // Normalize
        const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
        if (length > 0) {
            normal.x /= length;
            normal.y /= length;
            normal.z /= length;
        }
        
        return normal;
    }
}
