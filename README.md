# Terrain Synthesis - 3D Procedural Terrain Generator

An automated terrain synthesis model for generating realistic 3D terrain with rivers, valleys, and mountains using advanced computer graphics techniques.

## Features

- **Multi-octave Noise Generation**: Perlin and Simplex noise for base terrain
- **Hydraulic Erosion Simulation**: Realistic water flow and sediment transport
- **River Network Generation**: Flow accumulation and drainage patterns
- **Mountain Ridge Systems**: Ridge noise and domain warping
- **Real-time 3D Visualization**: Interactive Three.js rendering
- **Parameter Control**: Live adjustment of all generation parameters
- **Export Capabilities**: Heightmaps and 3D models

## Installation

1. **Install Node.js** (if not already installed):
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version` and `npm --version`

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Open in browser**:
   - Navigate to `http://localhost:3000`
   - Or use the alternative Python server: `npm run serve`

## Usage

### Controls
- **WASD**: Move camera
- **Mouse**: Look around (click to enable pointer lock)
- **Scroll**: Zoom in/out
- **Space**: Move up
- **Shift**: Move down

### Parameter Panels
- **Terrain**: Size and scale settings
- **Noise**: Multi-octave noise parameters
- **Erosion**: Hydraulic erosion simulation
- **Rivers**: River network generation
- **Visualization**: Display options

### Generation Process
1. Adjust parameters in the control panel
2. Click "Generate Terrain" to create new terrain
3. Experiment with different settings for various landscapes
4. Export heightmaps for use in other applications

## Technical Details

### Terrain Generation Pipeline
1. **Base Heightmap**: Multi-octave Perlin noise
2. **Erosion Simulation**: Particle-based hydraulic erosion
3. **River Extraction**: Flow accumulation and network analysis
4. **Mesh Generation**: 3D geometry with proper normals
5. **Visualization**: Height-based coloring and lighting

### Key Algorithms
- **Perlin Noise**: Coherent noise for natural-looking terrain
- **Flow Accumulation**: D8 algorithm for drainage analysis
- **Hydraulic Erosion**: Water simulation with sediment transport
- **Ridge Noise**: Inverted noise for mountain ridges

### File Structure
```
src/
├── main.js                 # Main application entry point
├── terrain/
│   └── TerrainGenerator.js # Core terrain generation
├── noise/
│   └── NoiseGenerator.js   # Noise functions (Perlin, Simplex, etc.)
├── erosion/
│   └── ErosionSimulator.js # Hydraulic erosion simulation
├── rivers/
│   └── RiverGenerator.js   # River network generation
└── controls/
    └── CameraController.js # 3D camera controls
```

## Customization

### Adding New Noise Types
Extend `NoiseGenerator.js` with new noise functions:
```javascript
customNoise(x, y, params) {
    // Your custom noise implementation
}
```

### Modifying Erosion
Adjust erosion parameters in `ErosionSimulator.js`:
- Water amount and flow velocity
- Sediment capacity and deposition
- Evaporation rates

### Terrain Features
- Modify `TerrainGenerator.js` for different terrain types
- Adjust height-based coloring in material creation
- Add custom geological features

## Performance Tips

- Start with smaller terrain sizes (256x256) for faster generation
- Reduce erosion iterations for quicker results
- Use wireframe mode for performance testing
- Lower octave count for simpler terrain

## Export Options

- **Heightmaps**: Grayscale images for external use
- **3D Models**: OBJ format (planned feature)
- **Parameter Sets**: Save/load terrain configurations

## Dependencies

- **Three.js**: 3D graphics library
- **dat.GUI**: Parameter control interface
- **Vite**: Development server and build tool

## Browser Compatibility

- Modern browsers with WebGL support
- Chrome, Firefox, Safari, Edge (latest versions)
- Mobile browsers (with reduced performance)

## Future Enhancements

- [ ] Thermal erosion simulation
- [ ] Vegetation placement
- [ ] Weather simulation
- [ ] Geological layering
- [ ] Real-time parameter updates
- [ ] Advanced export formats
- [ ] Texture generation
- [ ] Level-of-detail (LOD) system

## Contributing

Feel free to contribute improvements:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

MIT License - see LICENSE file for details.
