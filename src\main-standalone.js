// Standalone version that works with CDN libraries
// All classes included inline to avoid import issues

// Simple Noise Generator
class NoiseGenerator {
    constructor(seed = 12345) {
        this.seed = seed;
        this.permutation = this.generatePermutation();
    }

    generatePermutation() {
        const p = [];
        for (let i = 0; i < 256; i++) {
            p[i] = i;
        }

        let random = this.seededRandom(this.seed);
        for (let i = 255; i > 0; i--) {
            const j = Math.floor(random() * (i + 1));
            [p[i], p[j]] = [p[j], p[i]];
        }

        for (let i = 0; i < 256; i++) {
            p[256 + i] = p[i];
        }

        return p;
    }

    seededRandom(seed) {
        let x = Math.sin(seed) * 10000;
        return function() {
            x = Math.sin(x) * 10000;
            return x - Math.floor(x);
        };
    }

    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }

    lerp(a, b, t) {
        return a + t * (b - a);
    }

    grad(hash, x, y) {
        const h = hash & 3;
        const u = h < 2 ? x : y;
        const v = h < 2 ? y : x;
        return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
    }

    perlin(x, y) {
        const X = Math.floor(x) & 255;
        const Y = Math.floor(y) & 255;

        x -= Math.floor(x);
        y -= Math.floor(y);

        const u = this.fade(x);
        const v = this.fade(y);

        const A = this.permutation[X] + Y;
        const B = this.permutation[X + 1] + Y;

        return this.lerp(
            this.lerp(
                this.grad(this.permutation[A], x, y),
                this.grad(this.permutation[B], x - 1, y),
                u
            ),
            this.lerp(
                this.grad(this.permutation[A + 1], x, y - 1),
                this.grad(this.permutation[B + 1], x - 1, y - 1),
                u
            ),
            v
        );
    }
}

// Simple Erosion Simulator
class ErosionSimulator {
    simulate(heightData, params) {
        console.log('Starting erosion simulation...');

        const { width, height } = heightData;
        const { erosionIterations, erosionStrength } = params;

        const newHeightData = new Float32Array(heightData.data);

        // Simple erosion - just smooth the terrain slightly
        for (let iteration = 0; iteration < erosionIterations; iteration++) {
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const index = y * width + x;

                    // Average with neighbors
                    const avg = (
                        newHeightData[index - 1] +
                        newHeightData[index + 1] +
                        newHeightData[index - width] +
                        newHeightData[index + width]
                    ) / 4;

                    newHeightData[index] = newHeightData[index] * (1 - erosionStrength * 0.01) + avg * (erosionStrength * 0.01);
                }
            }
        }

        console.log('Erosion simulation complete');

        return {
            data: newHeightData,
            width: width,
            height: height
        };
    }
}

// Simple River Generator
class RiverGenerator {
    generate(heightData, params) {
        console.log('Generating river networks...');

        // For now, just return empty data
        console.log('River generation complete');

        return {
            flowAccumulation: null,
            riverNetwork: null
        };
    }
}

// Terrain Generator
class TerrainGenerator {
    constructor() {
        this.noiseGenerator = new NoiseGenerator();
        this.erosionSimulator = new ErosionSimulator();
        this.riverGenerator = new RiverGenerator();
        this.heightData = null;
        this.waterData = null;
    }

    generate(params) {
        console.log('Starting terrain generation with params:', params);

        // Step 1: Generate base heightmap using noise
        this.heightData = this.generateBaseHeightmap(params);

        // Step 2: Apply erosion simulation
        if (params.erosionIterations > 0) {
            this.heightData = this.erosionSimulator.simulate(this.heightData, params);
        }

        // Step 3: Generate river networks
        if (params.showRivers) {
            this.waterData = this.riverGenerator.generate(this.heightData, params);
        }

        // Step 4: Create 3D mesh
        const terrainMesh = this.createTerrainMesh(params);

        return terrainMesh;
    }

    generateBaseHeightmap(params) {
        const { width, height, noiseScale, octaves, persistence, lacunarity, amplitude } = params;
        const heightData = new Float32Array(width * height);

        console.log('Generating base heightmap...');

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;

                // Generate multi-octave noise
                let noiseValue = 0;
                let frequency = noiseScale;
                let currentAmplitude = amplitude;
                let maxValue = 0;

                for (let octave = 0; octave < octaves; octave++) {
                    noiseValue += this.noiseGenerator.perlin(
                        x * frequency,
                        y * frequency
                    ) * currentAmplitude;

                    maxValue += currentAmplitude;
                    currentAmplitude *= persistence;
                    frequency *= lacunarity;
                }

                // Normalize and store
                heightData[index] = noiseValue / maxValue;
            }
        }

        return {
            data: heightData,
            width: width,
            height: height
        };
    }

    createTerrainMesh(params) {
        const { width, height, scale } = params;
        const geometry = new THREE.PlaneGeometry(
            width * scale / width,
            height * scale / height,
            width - 1,
            height - 1
        );

        // Apply height data to vertices
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const x = Math.floor((vertices[i] / scale * width) + width / 2);
            const y = Math.floor((vertices[i + 1] / scale * height) + height / 2);
            const index = Math.max(0, Math.min(y * width + x, this.heightData.data.length - 1));
            vertices[i + 2] = this.heightData.data[index] * scale * 0.5;
        }

        // Apply vertex colors if height-based coloring is enabled
        if (params.colorByHeight) {
            this.applyVertexColors(geometry, this.heightData, params);
        }

        // Recalculate normals for proper lighting
        geometry.computeVertexNormals();

        // Create material with height-based coloring
        const material = this.createTerrainMaterial(params);

        // Create mesh
        const mesh = new THREE.Mesh(geometry, material);
        mesh.rotation.x = -Math.PI / 2; // Rotate to make it horizontal
        mesh.receiveShadow = true;
        mesh.castShadow = true;

        return mesh;
    }

    createTerrainMaterial(params) {
        if (params.colorByHeight) {
            return new THREE.MeshLambertMaterial({
                vertexColors: true,
                wireframe: params.wireframe
            });
        } else {
            return new THREE.MeshLambertMaterial({
                color: 0x8fbc8f,
                wireframe: params.wireframe
            });
        }
    }

    applyVertexColors(geometry, heightData, params) {
        const colors = [];
        const vertices = geometry.attributes.position.array;

        // Find min and max heights for normalization
        let minHeight = Infinity;
        let maxHeight = -Infinity;

        for (let i = 0; i < heightData.data.length; i++) {
            minHeight = Math.min(minHeight, heightData.data[i]);
            maxHeight = Math.max(maxHeight, heightData.data[i]);
        }

        const heightRange = maxHeight - minHeight;

        for (let i = 0; i < vertices.length; i += 3) {
            const height = vertices[i + 2] / (params.scale * 0.5);
            const normalizedHeight = heightRange > 0 ? (height - minHeight) / heightRange : 0;

            // Create color based on height
            let color = new THREE.Color();

            if (normalizedHeight < 0.2) {
                color.setRGB(0.1, 0.3, 0.8); // Water - blue
            } else if (normalizedHeight < 0.4) {
                color.setRGB(0.8, 0.7, 0.5); // Beach/shore - sandy
            } else if (normalizedHeight < 0.6) {
                color.setRGB(0.2, 0.6, 0.2); // Grass - green
            } else if (normalizedHeight < 0.8) {
                color.setRGB(0.5, 0.4, 0.3); // Rock - gray/brown
            } else {
                color.setRGB(0.9, 0.9, 0.9); // Snow - white
            }

            colors.push(color.r, color.g, color.b);
        }

        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
    }
}

// Camera Controller
class CameraController {
    constructor(camera, domElement) {
        this.camera = camera;
        this.domElement = domElement;

        this.moveState = {
            forward: false, backward: false, left: false, right: false, up: false, down: false
        };

        this.mouseState = { isDown: false, lastX: 0, lastY: 0 };
        this.pitch = 0;
        this.yaw = 0;
        this.moveSpeed = 50;
        this.mouseSensitivity = 0.002;
        this.zoomSpeed = 5;

        this.forward = new THREE.Vector3();
        this.right = new THREE.Vector3();
        this.up = new THREE.Vector3(0, 1, 0);

        this.setupEventListeners();
        this.updateCameraRotation();
    }

    setupEventListeners() {
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));

        this.domElement.addEventListener('mousedown', (event) => this.onMouseDown(event));
        this.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        this.domElement.addEventListener('mouseup', (event) => this.onMouseUp(event));
        this.domElement.addEventListener('wheel', (event) => this.onWheel(event));
        this.domElement.addEventListener('contextmenu', (event) => event.preventDefault());

        this.domElement.addEventListener('click', () => {
            this.domElement.requestPointerLock();
        });

        document.addEventListener('pointerlockchange', () => {
            if (document.pointerLockElement === this.domElement) {
                document.addEventListener('mousemove', this.onPointerLockMove.bind(this));
            } else {
                document.removeEventListener('mousemove', this.onPointerLockMove.bind(this));
            }
        });
    }

    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW': case 'ArrowUp': this.moveState.forward = true; break;
            case 'KeyS': case 'ArrowDown': this.moveState.backward = true; break;
            case 'KeyA': case 'ArrowLeft': this.moveState.left = true; break;
            case 'KeyD': case 'ArrowRight': this.moveState.right = true; break;
            case 'Space': this.moveState.up = true; event.preventDefault(); break;
            case 'ShiftLeft': case 'ShiftRight': this.moveState.down = true; break;
        }
    }

    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW': case 'ArrowUp': this.moveState.forward = false; break;
            case 'KeyS': case 'ArrowDown': this.moveState.backward = false; break;
            case 'KeyA': case 'ArrowLeft': this.moveState.left = false; break;
            case 'KeyD': case 'ArrowRight': this.moveState.right = false; break;
            case 'Space': this.moveState.up = false; break;
            case 'ShiftLeft': case 'ShiftRight': this.moveState.down = false; break;
        }
    }

    onMouseDown(event) {
        this.mouseState.isDown = true;
        this.mouseState.lastX = event.clientX;
        this.mouseState.lastY = event.clientY;
    }

    onMouseMove(event) {
        if (!this.mouseState.isDown) return;

        const deltaX = event.clientX - this.mouseState.lastX;
        const deltaY = event.clientY - this.mouseState.lastY;

        this.yaw -= deltaX * this.mouseSensitivity;
        this.pitch -= deltaY * this.mouseSensitivity;
        this.pitch = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.pitch));

        this.updateCameraRotation();

        this.mouseState.lastX = event.clientX;
        this.mouseState.lastY = event.clientY;
    }

    onPointerLockMove(event) {
        const deltaX = event.movementX;
        const deltaY = event.movementY;

        this.yaw -= deltaX * this.mouseSensitivity;
        this.pitch -= deltaY * this.mouseSensitivity;
        this.pitch = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.pitch));

        this.updateCameraRotation();
    }

    onMouseUp(event) {
        this.mouseState.isDown = false;
    }

    onWheel(event) {
        const delta = event.deltaY > 0 ? 1 : -1;
        this.camera.getWorldDirection(this.forward);
        this.camera.position.addScaledVector(this.forward, delta * this.zoomSpeed);
        event.preventDefault();
    }

    updateCameraRotation() {
        const quaternion = new THREE.Quaternion();
        quaternion.setFromEuler(new THREE.Euler(this.pitch, this.yaw, 0, 'YXZ'));
        this.camera.quaternion.copy(quaternion);
    }

    update() {
        const deltaTime = 0.016;
        const moveDistance = this.moveSpeed * deltaTime;

        this.camera.getWorldDirection(this.forward);
        this.right.crossVectors(this.forward, this.up).normalize();

        if (this.moveState.forward) this.camera.position.addScaledVector(this.forward, moveDistance);
        if (this.moveState.backward) this.camera.position.addScaledVector(this.forward, -moveDistance);
        if (this.moveState.left) this.camera.position.addScaledVector(this.right, -moveDistance);
        if (this.moveState.right) this.camera.position.addScaledVector(this.right, moveDistance);
        if (this.moveState.up) this.camera.position.addScaledVector(this.up, moveDistance);
        if (this.moveState.down) this.camera.position.addScaledVector(this.up, -moveDistance);
    }
}

// Export Utils
class ExportUtils {
    static exportHeightmapPNG(heightData, filename = 'heightmap.png') {
        const { width, height, data } = heightData;
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext('2d');
        const imageData = context.createImageData(width, height);

        let min = Infinity, max = -Infinity;
        for (let i = 0; i < data.length; i++) {
            min = Math.min(min, data[i]);
            max = Math.max(max, data[i]);
        }

        const range = max - min;

        for (let i = 0; i < data.length; i++) {
            const normalizedValue = range > 0 ? (data[i] - min) / range : 0;
            const grayValue = Math.floor(normalizedValue * 255);
            const pixelIndex = i * 4;

            imageData.data[pixelIndex] = grayValue;
            imageData.data[pixelIndex + 1] = grayValue;
            imageData.data[pixelIndex + 2] = grayValue;
            imageData.data[pixelIndex + 3] = 255;
        }

        context.putImageData(imageData, 0, 0);

        const link = document.createElement('a');
        link.download = filename;
        link.href = canvas.toDataURL('image/png');
        link.click();

        return canvas.toDataURL('image/png');
    }

    static exportTerrainOBJ(heightData, scale = 1, filename = 'terrain.obj') {
        const { width, height, data } = heightData;
        let objContent = '# Terrain OBJ file\n';
        objContent += `# Generated terrain ${width}x${height}\n\n`;

        // Write vertices
        objContent += '# Vertices\n';
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                const worldX = (x - width / 2) * scale;
                const worldY = data[index] * scale;
                const worldZ = (y - height / 2) * scale;
                objContent += `v ${worldX.toFixed(6)} ${worldY.toFixed(6)} ${worldZ.toFixed(6)}\n`;
            }
        }

        // Write faces (simplified)
        objContent += '\n# Faces\n';
        for (let y = 0; y < height - 1; y++) {
            for (let x = 0; x < width - 1; x++) {
                const i1 = y * width + x + 1;
                const i2 = (y + 1) * width + x + 1;
                const i3 = (y + 1) * width + (x + 1) + 1;
                const i4 = y * width + (x + 1) + 1;

                objContent += `f ${i1} ${i2} ${i3}\n`;
                objContent += `f ${i1} ${i3} ${i4}\n`;
            }
        }

        const blob = new Blob([objContent], { type: 'text/plain' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();

        return objContent;
    }

    static exportParameters(params, filename = 'terrain_params.json') {
        const jsonContent = JSON.stringify(params, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();

        return jsonContent;
    }
}

class TerrainSynthesis {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.terrainGenerator = null;
        this.cameraController = null;
        this.gui = null;
        
        // Terrain parameters
        this.params = {
            // Terrain size
            width: 256,
            height: 256,
            scale: 100,
            
            // Noise parameters
            noiseScale: 0.01,
            octaves: 6,
            persistence: 0.5,
            lacunarity: 2.0,
            amplitude: 50,
            
            // Erosion parameters
            erosionIterations: 50,
            erosionStrength: 0.1,
            evaporationRate: 0.01,
            
            // River parameters
            riverThreshold: 0.1,
            riverWidth: 2,
            riverDepth: 5,
            
            // Visualization
            wireframe: false,
            showRivers: true,
            colorByHeight: true,
            
            // Actions
            generateTerrain: () => this.generateTerrain(),
            exportHeightmapPNG: () => this.exportHeightmapPNG(),
            exportTerrainOBJ: () => this.exportTerrainOBJ(),
            exportParameters: () => this.exportParameters(),
            randomSeed: () => this.randomizeSeed()
        };
        
        this.init();
    }
    
    init() {
        try {
            console.log('Initializing Terrain Synthesis...');
            
            this.setupScene();
            this.setupRenderer();
            this.setupCamera();
            this.setupLighting();
            this.setupControls();
            this.setupGUI();
            
            // Generate initial terrain
            this.generateTerrain();
            
            // Start render loop
            this.animate();
            
            // Hide loading screen
            document.getElementById('loading').style.display = 'none';
            
            console.log('Terrain Synthesis initialized successfully!');
        } catch (error) {
            console.error('Error initializing application:', error);
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = error.message;
        }
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        this.scene.fog = new THREE.Fog(0x87CEEB, 100, 1000);
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(this.renderer.domElement);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            2000
        );
        this.camera.position.set(0, 100, 100);
    }
    
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -200;
        directionalLight.shadow.camera.right = 200;
        directionalLight.shadow.camera.top = 200;
        directionalLight.shadow.camera.bottom = -200;
        this.scene.add(directionalLight);
    }
    
    setupControls() {
        this.cameraController = new CameraController(this.camera, this.renderer.domElement);
    }
    
    setupGUI() {
        this.gui = new dat.GUI({ autoPlace: false });
        document.getElementById('controls').appendChild(this.gui.domElement);
        
        // Terrain folder
        const terrainFolder = this.gui.addFolder('Terrain');
        terrainFolder.add(this.params, 'width', 64, 512, 64);
        terrainFolder.add(this.params, 'height', 64, 512, 64);
        terrainFolder.add(this.params, 'scale', 10, 200, 10);
        
        // Noise folder
        const noiseFolder = this.gui.addFolder('Noise');
        noiseFolder.add(this.params, 'noiseScale', 0.001, 0.1, 0.001);
        noiseFolder.add(this.params, 'octaves', 1, 10, 1);
        noiseFolder.add(this.params, 'persistence', 0.1, 1.0, 0.1);
        noiseFolder.add(this.params, 'lacunarity', 1.0, 4.0, 0.1);
        noiseFolder.add(this.params, 'amplitude', 10, 200, 5);
        
        // Erosion folder
        const erosionFolder = this.gui.addFolder('Erosion');
        erosionFolder.add(this.params, 'erosionIterations', 0, 200, 10);
        erosionFolder.add(this.params, 'erosionStrength', 0.01, 1.0, 0.01);
        erosionFolder.add(this.params, 'evaporationRate', 0.001, 0.1, 0.001);
        
        // Rivers folder
        const riversFolder = this.gui.addFolder('Rivers');
        riversFolder.add(this.params, 'riverThreshold', 0.01, 1.0, 0.01);
        riversFolder.add(this.params, 'riverWidth', 1, 10, 1);
        riversFolder.add(this.params, 'riverDepth', 1, 20, 1);
        
        // Visualization folder
        const vizFolder = this.gui.addFolder('Visualization');
        vizFolder.add(this.params, 'wireframe').onChange(() => this.updateVisualization());
        vizFolder.add(this.params, 'showRivers').onChange(() => this.updateVisualization());
        vizFolder.add(this.params, 'colorByHeight').onChange(() => this.updateVisualization());
        
        // Actions
        const actionsFolder = this.gui.addFolder('Actions');
        actionsFolder.add(this.params, 'generateTerrain');
        actionsFolder.add(this.params, 'randomSeed');
        actionsFolder.add(this.params, 'exportHeightmapPNG');
        actionsFolder.add(this.params, 'exportTerrainOBJ');
        actionsFolder.add(this.params, 'exportParameters');
        actionsFolder.open();
        
        // Open folders by default
        terrainFolder.open();
        noiseFolder.open();
    }
    
    generateTerrain() {
        try {
            console.log('Generating terrain...');
            
            // Initialize generators if not already done
            if (!this.terrainGenerator) {
                this.terrainGenerator = new TerrainGenerator();
            }
            
            // Generate terrain with current parameters
            const terrain = this.terrainGenerator.generate(this.params);
            
            // Remove existing terrain from scene
            const existingTerrain = this.scene.getObjectByName('terrain');
            if (existingTerrain) {
                this.scene.remove(existingTerrain);
            }
            
            // Add new terrain to scene
            terrain.name = 'terrain';
            this.scene.add(terrain);
            
            console.log('Terrain generation complete');
        } catch (error) {
            console.error('Error generating terrain:', error);
            alert('Error generating terrain: ' + error.message);
        }
    }
    
    updateVisualization() {
        const terrain = this.scene.getObjectByName('terrain');
        if (terrain) {
            terrain.material.wireframe = this.params.wireframe;
        }
    }
    
    exportHeightmapPNG() {
        if (this.terrainGenerator && this.terrainGenerator.heightData) {
            ExportUtils.exportHeightmapPNG(this.terrainGenerator.heightData);
        } else {
            alert('Please generate terrain first!');
        }
    }
    
    exportTerrainOBJ() {
        if (this.terrainGenerator && this.terrainGenerator.heightData) {
            ExportUtils.exportTerrainOBJ(this.terrainGenerator.heightData, this.params.scale / 10);
        } else {
            alert('Please generate terrain first!');
        }
    }
    
    exportParameters() {
        ExportUtils.exportParameters(this.params);
    }
    
    randomizeSeed() {
        // Randomize noise parameters for different terrain
        this.params.noiseScale = 0.005 + Math.random() * 0.02;
        this.params.octaves = 4 + Math.floor(Math.random() * 4);
        this.params.persistence = 0.3 + Math.random() * 0.4;
        this.params.lacunarity = 1.5 + Math.random() * 1.0;
        this.params.amplitude = 30 + Math.random() * 40;
        
        // Update GUI display
        this.gui.updateDisplay();
        
        // Generate new terrain
        this.generateTerrain();
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.cameraController) {
            this.cameraController.update();
        }
        
        this.renderer.render(this.scene, this.camera);
    }
}

// Start the application
console.log('Starting Terrain Synthesis application...');
new TerrainSynthesis();
