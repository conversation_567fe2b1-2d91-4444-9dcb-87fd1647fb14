export class RiverGenerator {
    constructor() {
        this.flowAccumulation = null;
        this.flowDirection = null;
        this.riverNetwork = null;
    }
    
    generate(heightData, params) {
        console.log('Generating river networks...');
        
        const { width, height } = heightData;
        const { riverThreshold, riverWidth, riverDepth } = params;
        
        // Calculate flow accumulation
        this.calculateFlowAccumulation(heightData, width, height);
        
        // Extract river network
        this.extractRiverNetwork(width, height, riverThreshold);
        
        // Carve rivers into terrain
        this.carveRivers(heightData, width, height, riverWidth, riverDepth);
        
        console.log('River generation complete');
        
        return {
            flowAccumulation: this.flowAccumulation,
            riverNetwork: this.riverNetwork
        };
    }
    
    calculateFlowAccumulation(heightData, width, height) {
        this.flowAccumulation = new Float32Array(width * height);
        this.flowDirection = new Int8Array(width * height);
        
        // Initialize flow accumulation to 1 (each cell contributes itself)
        this.flowAccumulation.fill(1);
        
        // Calculate flow directions using D8 algorithm
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const index = y * width + x;
                const currentHeight = heightData.data[index];
                
                let steepestSlope = 0;
                let flowDir = 0;
                
                // Check 8 neighbors
                const neighbors = [
                    [-1, -1], [0, -1], [1, -1],
                    [-1,  0],          [1,  0],
                    [-1,  1], [0,  1], [1,  1]
                ];
                
                for (let i = 0; i < neighbors.length; i++) {
                    const [dx, dy] = neighbors[i];
                    const nx = x + dx;
                    const ny = y + dy;
                    
                    if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                        const neighborIndex = ny * width + nx;
                        const neighborHeight = heightData.data[neighborIndex];
                        
                        // Calculate slope
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const slope = (currentHeight - neighborHeight) / distance;
                        
                        if (slope > steepestSlope) {
                            steepestSlope = slope;
                            flowDir = i + 1; // 1-8 for the 8 directions
                        }
                    }
                }
                
                this.flowDirection[index] = flowDir;
            }
        }
        
        // Calculate flow accumulation by processing cells in order of decreasing elevation
        const cellsByElevation = [];
        for (let i = 0; i < width * height; i++) {
            cellsByElevation.push({ index: i, elevation: heightData.data[i] });
        }
        
        // Sort by elevation (highest first)
        cellsByElevation.sort((a, b) => b.elevation - a.elevation);
        
        // Process cells in order
        for (const cell of cellsByElevation) {
            const index = cell.index;
            const x = index % width;
            const y = Math.floor(index / width);
            
            const flowDir = this.flowDirection[index];
            if (flowDir > 0) {
                // Get flow direction
                const neighbors = [
                    [-1, -1], [0, -1], [1, -1],
                    [-1,  0],          [1,  0],
                    [-1,  1], [0,  1], [1,  1]
                ];
                
                const [dx, dy] = neighbors[flowDir - 1];
                const nx = x + dx;
                const ny = y + dy;
                
                if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                    const downstreamIndex = ny * width + nx;
                    this.flowAccumulation[downstreamIndex] += this.flowAccumulation[index];
                }
            }
        }
    }
    
    extractRiverNetwork(width, height, threshold) {
        this.riverNetwork = new Uint8Array(width * height);
        
        // Find maximum flow accumulation for normalization
        let maxFlow = 0;
        for (let i = 0; i < this.flowAccumulation.length; i++) {
            maxFlow = Math.max(maxFlow, this.flowAccumulation[i]);
        }
        
        // Mark river cells
        for (let i = 0; i < width * height; i++) {
            const normalizedFlow = this.flowAccumulation[i] / maxFlow;
            if (normalizedFlow > threshold) {
                this.riverNetwork[i] = 1;
            }
        }
        
        // Clean up river network (remove isolated pixels, connect segments)
        this.cleanRiverNetwork(width, height);
    }
    
    cleanRiverNetwork(width, height) {
        const newRiverNetwork = new Uint8Array(this.riverNetwork);
        
        // Remove isolated pixels
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const index = y * width + x;
                
                if (this.riverNetwork[index] === 1) {
                    // Count river neighbors
                    let riverNeighbors = 0;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            if (dx === 0 && dy === 0) continue;
                            const neighborIndex = (y + dy) * width + (x + dx);
                            if (this.riverNetwork[neighborIndex] === 1) {
                                riverNeighbors++;
                            }
                        }
                    }
                    
                    // Remove isolated pixels
                    if (riverNeighbors === 0) {
                        newRiverNetwork[index] = 0;
                    }
                }
            }
        }
        
        this.riverNetwork = newRiverNetwork;
    }
    
    carveRivers(heightData, width, height, riverWidth, riverDepth) {
        // Carve rivers into the terrain
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                
                if (this.riverNetwork[index] === 1) {
                    // Carve river channel
                    const carveRadius = Math.floor(riverWidth / 2);
                    
                    for (let dy = -carveRadius; dy <= carveRadius; dy++) {
                        for (let dx = -carveRadius; dx <= carveRadius; dx++) {
                            const nx = x + dx;
                            const ny = y + dy;
                            
                            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                                const neighborIndex = ny * width + nx;
                                const distance = Math.sqrt(dx * dx + dy * dy);
                                
                                if (distance <= carveRadius) {
                                    // Calculate carve amount based on distance from center
                                    const carveAmount = riverDepth * (1 - distance / carveRadius);
                                    heightData.data[neighborIndex] -= carveAmount * 0.1;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Get river data for visualization
    getRiverMesh(width, height, scale) {
        // This would create a separate mesh for rivers
        // For now, rivers are carved into the main terrain
        return null;
    }
    
    // Export river network as image
    exportRiverNetwork(width, height) {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext('2d');
        const imageData = context.createImageData(width, height);
        
        for (let i = 0; i < this.riverNetwork.length; i++) {
            const pixelIndex = i * 4;
            const value = this.riverNetwork[i] * 255;
            imageData.data[pixelIndex] = 0;       // R
            imageData.data[pixelIndex + 1] = 0;   // G
            imageData.data[pixelIndex + 2] = value; // B (blue for rivers)
            imageData.data[pixelIndex + 3] = 255; // A
        }
        
        context.putImageData(imageData, 0, 0);
        return canvas.toDataURL();
    }
}
