import * as THREE from 'three';

export class CameraController {
    constructor(camera, domElement) {
        this.camera = camera;
        this.domElement = domElement;
        
        // Movement state
        this.moveState = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            up: false,
            down: false
        };
        
        // Mouse state
        this.mouseState = {
            isDown: false,
            lastX: 0,
            lastY: 0
        };
        
        // Camera rotation
        this.pitch = 0;
        this.yaw = 0;
        
        // Movement settings
        this.moveSpeed = 50;
        this.mouseSensitivity = 0.002;
        this.zoomSpeed = 5;
        
        // Temporary vectors for calculations
        this.forward = new THREE.Vector3();
        this.right = new THREE.Vector3();
        this.up = new THREE.Vector3(0, 1, 0);
        
        this.setupEventListeners();
        this.updateCameraRotation();
    }
    
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        
        // Mouse events
        this.domElement.addEventListener('mousedown', (event) => this.onMouseDown(event));
        this.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        this.domElement.addEventListener('mouseup', (event) => this.onMouseUp(event));
        this.domElement.addEventListener('wheel', (event) => this.onWheel(event));
        
        // Prevent context menu
        this.domElement.addEventListener('contextmenu', (event) => event.preventDefault());
        
        // Pointer lock for better mouse control
        this.domElement.addEventListener('click', () => {
            this.domElement.requestPointerLock();
        });
        
        document.addEventListener('pointerlockchange', () => {
            if (document.pointerLockElement === this.domElement) {
                document.addEventListener('mousemove', this.onPointerLockMove.bind(this));
            } else {
                document.removeEventListener('mousemove', this.onPointerLockMove.bind(this));
            }
        });
    }
    
    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.moveState.forward = true;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.moveState.backward = true;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.moveState.left = true;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.moveState.right = true;
                break;
            case 'Space':
                this.moveState.up = true;
                event.preventDefault();
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.moveState.down = true;
                break;
        }
    }
    
    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.moveState.forward = false;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.moveState.backward = false;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.moveState.left = false;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.moveState.right = false;
                break;
            case 'Space':
                this.moveState.up = false;
                break;
            case 'ShiftLeft':
            case 'ShiftRight':
                this.moveState.down = false;
                break;
        }
    }
    
    onMouseDown(event) {
        this.mouseState.isDown = true;
        this.mouseState.lastX = event.clientX;
        this.mouseState.lastY = event.clientY;
    }
    
    onMouseMove(event) {
        if (!this.mouseState.isDown) return;
        
        const deltaX = event.clientX - this.mouseState.lastX;
        const deltaY = event.clientY - this.mouseState.lastY;
        
        this.yaw -= deltaX * this.mouseSensitivity;
        this.pitch -= deltaY * this.mouseSensitivity;
        
        // Clamp pitch to prevent flipping
        this.pitch = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.pitch));
        
        this.updateCameraRotation();
        
        this.mouseState.lastX = event.clientX;
        this.mouseState.lastY = event.clientY;
    }
    
    onPointerLockMove(event) {
        const deltaX = event.movementX;
        const deltaY = event.movementY;
        
        this.yaw -= deltaX * this.mouseSensitivity;
        this.pitch -= deltaY * this.mouseSensitivity;
        
        // Clamp pitch to prevent flipping
        this.pitch = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.pitch));
        
        this.updateCameraRotation();
    }
    
    onMouseUp(event) {
        this.mouseState.isDown = false;
    }
    
    onWheel(event) {
        const delta = event.deltaY > 0 ? 1 : -1;
        
        // Move camera forward/backward based on scroll
        this.camera.getWorldDirection(this.forward);
        this.camera.position.addScaledVector(this.forward, delta * this.zoomSpeed);
        
        event.preventDefault();
    }
    
    updateCameraRotation() {
        // Create rotation quaternion from yaw and pitch
        const quaternion = new THREE.Quaternion();
        quaternion.setFromEuler(new THREE.Euler(this.pitch, this.yaw, 0, 'YXZ'));
        
        // Apply rotation to camera
        this.camera.quaternion.copy(quaternion);
    }
    
    update() {
        const deltaTime = 0.016; // Assume 60 FPS
        const moveDistance = this.moveSpeed * deltaTime;
        
        // Get camera direction vectors
        this.camera.getWorldDirection(this.forward);
        this.right.crossVectors(this.forward, this.up).normalize();
        
        // Apply movement
        if (this.moveState.forward) {
            this.camera.position.addScaledVector(this.forward, moveDistance);
        }
        if (this.moveState.backward) {
            this.camera.position.addScaledVector(this.forward, -moveDistance);
        }
        if (this.moveState.left) {
            this.camera.position.addScaledVector(this.right, -moveDistance);
        }
        if (this.moveState.right) {
            this.camera.position.addScaledVector(this.right, moveDistance);
        }
        if (this.moveState.up) {
            this.camera.position.addScaledVector(this.up, moveDistance);
        }
        if (this.moveState.down) {
            this.camera.position.addScaledVector(this.up, -moveDistance);
        }
    }
    
    // Utility methods
    setPosition(x, y, z) {
        this.camera.position.set(x, y, z);
    }
    
    lookAt(x, y, z) {
        const target = new THREE.Vector3(x, y, z);
        const direction = target.sub(this.camera.position).normalize();
        
        this.yaw = Math.atan2(direction.x, direction.z);
        this.pitch = Math.asin(-direction.y);
        
        this.updateCameraRotation();
    }
    
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    }
    
    setMouseSensitivity(sensitivity) {
        this.mouseSensitivity = sensitivity;
    }
}
