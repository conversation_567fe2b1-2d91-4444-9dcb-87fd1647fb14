// Import statements removed for standalone version
// THREE is available globally from CDN

export class TerrainGenerator {
    constructor() {
        this.noiseGenerator = new NoiseGenerator();
        this.erosionSimulator = new ErosionSimulator();
        this.riverGenerator = new RiverGenerator();
        this.heightData = null;
        this.waterData = null;
    }
    
    generate(params) {
        console.log('Starting terrain generation with params:', params);
        
        // Step 1: Generate base heightmap using noise
        this.heightData = this.generateBaseHeightmap(params);
        
        // Step 2: Apply erosion simulation
        if (params.erosionIterations > 0) {
            this.heightData = this.erosionSimulator.simulate(this.heightData, params);
        }
        
        // Step 3: Generate river networks
        if (params.showRivers) {
            this.waterData = this.riverGenerator.generate(this.heightData, params);
        }
        
        // Step 4: Create 3D mesh
        const terrainMesh = this.createTerrainMesh(params);
        
        return terrainMesh;
    }
    
    generateBaseHeightmap(params) {
        const { width, height, noiseScale, octaves, persistence, lacunarity, amplitude } = params;
        const heightData = new Float32Array(width * height);
        
        console.log('Generating base heightmap...');
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                
                // Generate multi-octave noise
                let noiseValue = 0;
                let frequency = noiseScale;
                let currentAmplitude = amplitude;
                let maxValue = 0;
                
                for (let octave = 0; octave < octaves; octave++) {
                    noiseValue += this.noiseGenerator.perlin(
                        x * frequency, 
                        y * frequency
                    ) * currentAmplitude;
                    
                    maxValue += currentAmplitude;
                    currentAmplitude *= persistence;
                    frequency *= lacunarity;
                }
                
                // Normalize and store
                heightData[index] = noiseValue / maxValue;
            }
        }
        
        return {
            data: heightData,
            width: width,
            height: height
        };
    }
    
    createTerrainMesh(params) {
        const { width, height, scale } = params;
        const geometry = new THREE.PlaneGeometry(
            width * scale / width, 
            height * scale / height, 
            width - 1, 
            height - 1
        );
        
        // Apply height data to vertices
        const vertices = geometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const x = Math.floor((vertices[i] / scale * width) + width / 2);
            const y = Math.floor((vertices[i + 1] / scale * height) + height / 2);
            const index = Math.max(0, Math.min(y * width + x, this.heightData.data.length - 1));
            vertices[i + 2] = this.heightData.data[index] * scale * 0.5;
        }
        
        // Apply vertex colors if height-based coloring is enabled
        if (params.colorByHeight) {
            this.applyVertexColors(geometry, this.heightData, params);
        }

        // Recalculate normals for proper lighting
        geometry.computeVertexNormals();

        // Create material with height-based coloring
        const material = this.createTerrainMaterial(params);

        // Create mesh
        const mesh = new THREE.Mesh(geometry, material);
        mesh.rotation.x = -Math.PI / 2; // Rotate to make it horizontal
        mesh.receiveShadow = true;
        mesh.castShadow = true;

        return mesh;
    }
    
    createTerrainMaterial(params) {
        if (params.colorByHeight) {
            // Create a height-based gradient material using vertex colors
            const material = new THREE.MeshLambertMaterial({
                vertexColors: true,
                wireframe: params.wireframe
            });

            return material;
        } else {
            return new THREE.MeshLambertMaterial({
                color: 0x8fbc8f,
                wireframe: params.wireframe
            });
        }
    }

    applyVertexColors(geometry, heightData, params) {
        const colors = [];
        const vertices = geometry.attributes.position.array;

        // Find min and max heights for normalization
        let minHeight = Infinity;
        let maxHeight = -Infinity;

        for (let i = 0; i < heightData.data.length; i++) {
            minHeight = Math.min(minHeight, heightData.data[i]);
            maxHeight = Math.max(maxHeight, heightData.data[i]);
        }

        const heightRange = maxHeight - minHeight;

        for (let i = 0; i < vertices.length; i += 3) {
            const height = vertices[i + 2] / (params.scale * 0.5); // Normalize height
            const normalizedHeight = (height - minHeight) / heightRange;

            // Create color based on height
            let color = new THREE.Color();

            if (normalizedHeight < 0.2) {
                // Water - blue
                color.setRGB(0.1, 0.3, 0.8);
            } else if (normalizedHeight < 0.4) {
                // Beach/shore - sandy
                color.setRGB(0.8, 0.7, 0.5);
            } else if (normalizedHeight < 0.6) {
                // Grass - green
                color.setRGB(0.2, 0.6, 0.2);
            } else if (normalizedHeight < 0.8) {
                // Rock - gray/brown
                color.setRGB(0.5, 0.4, 0.3);
            } else {
                // Snow - white
                color.setRGB(0.9, 0.9, 0.9);
            }

            colors.push(color.r, color.g, color.b);
        }

        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
    }
    
    getHeightAt(x, y) {
        if (!this.heightData) return 0;
        
        const { width, height } = this.heightData;
        x = Math.max(0, Math.min(x, width - 1));
        y = Math.max(0, Math.min(y, height - 1));
        
        const index = Math.floor(y) * width + Math.floor(x);
        return this.heightData.data[index];
    }
    
    exportHeightmap() {
        if (!this.heightData) return null;
        
        const { width, height, data } = this.heightData;
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext('2d');
        const imageData = context.createImageData(width, height);
        
        for (let i = 0; i < data.length; i++) {
            const value = Math.floor((data[i] + 1) * 127.5); // Normalize to 0-255
            const pixelIndex = i * 4;
            imageData.data[pixelIndex] = value;     // R
            imageData.data[pixelIndex + 1] = value; // G
            imageData.data[pixelIndex + 2] = value; // B
            imageData.data[pixelIndex + 3] = 255;   // A
        }
        
        context.putImageData(imageData, 0, 0);
        return canvas.toDataURL();
    }
}
