export class ErosionSimulator {
    constructor() {
        this.waterMap = null;
        this.velocityMap = null;
        this.sedimentMap = null;
    }
    
    simulate(heightData, params) {
        console.log('Starting erosion simulation...');
        
        const { width, height } = heightData;
        const { erosionIterations, erosionStrength, evaporationRate } = params;
        
        // Initialize maps
        this.waterMap = new Float32Array(width * height);
        this.velocityMap = new Float32Array(width * height * 2); // x, y components
        this.sedimentMap = new Float32Array(width * height);
        
        // Copy height data for modification
        const newHeightData = new Float32Array(heightData.data);
        
        // Run erosion iterations
        for (let iteration = 0; iteration < erosionIterations; iteration++) {
            this.simulateStep(newHeightData, width, height, params);
            
            if (iteration % 10 === 0) {
                console.log(`Erosion iteration ${iteration}/${erosionIterations}`);
            }
        }
        
        console.log('Erosion simulation complete');
        
        return {
            data: newHeightData,
            width: width,
            height: height,
            waterMap: this.waterMap,
            sedimentMap: this.sedimentMap
        };
    }
    
    simulateStep(heightData, width, height, params) {
        const { erosionStrength, evaporationRate } = params;
        
        // Add water (rainfall)
        this.addRainfall(width, height, 0.01);
        
        // Calculate flow velocities
        this.calculateFlow(heightData, width, height);
        
        // Erode terrain
        this.erode(heightData, width, height, erosionStrength);
        
        // Transport sediment
        this.transportSediment(width, height);
        
        // Evaporate water
        this.evaporateWater(width, height, evaporationRate);
    }
    
    addRainfall(width, height, amount) {
        for (let i = 0; i < width * height; i++) {
            this.waterMap[i] += amount;
        }
    }
    
    calculateFlow(heightData, width, height) {
        const newVelocityMap = new Float32Array(this.velocityMap.length);
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const index = y * width + x;
                const waterHeight = heightData[index] + this.waterMap[index];
                
                // Calculate gradients to neighbors
                const gradX = (
                    (heightData[index + 1] + this.waterMap[index + 1]) -
                    (heightData[index - 1] + this.waterMap[index - 1])
                ) / 2;
                
                const gradY = (
                    (heightData[index + width] + this.waterMap[index + width]) -
                    (heightData[index - width] + this.waterMap[index - width])
                ) / 2;
                
                // Update velocity based on gradient (simplified)
                const velocityIndex = index * 2;
                newVelocityMap[velocityIndex] = this.velocityMap[velocityIndex] - gradX * 0.1;
                newVelocityMap[velocityIndex + 1] = this.velocityMap[velocityIndex + 1] - gradY * 0.1;
                
                // Apply damping
                newVelocityMap[velocityIndex] *= 0.9;
                newVelocityMap[velocityIndex + 1] *= 0.9;
            }
        }
        
        this.velocityMap = newVelocityMap;
    }
    
    erode(heightData, width, height, strength) {
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const index = y * width + x;
                const velocityIndex = index * 2;
                
                // Calculate erosion based on water amount and velocity
                const waterAmount = this.waterMap[index];
                const velocity = Math.sqrt(
                    this.velocityMap[velocityIndex] * this.velocityMap[velocityIndex] +
                    this.velocityMap[velocityIndex + 1] * this.velocityMap[velocityIndex + 1]
                );
                
                const erosionAmount = Math.min(
                    waterAmount * velocity * strength,
                    heightData[index] * 0.01 // Limit erosion per step
                );
                
                // Erode terrain
                heightData[index] -= erosionAmount;
                
                // Add to sediment
                this.sedimentMap[index] += erosionAmount;
            }
        }
    }
    
    transportSediment(width, height) {
        const newSedimentMap = new Float32Array(this.sedimentMap.length);
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const index = y * width + x;
                const velocityIndex = index * 2;
                
                // Transport sediment based on velocity
                const vx = this.velocityMap[velocityIndex];
                const vy = this.velocityMap[velocityIndex + 1];
                
                // Simple sediment transport (could be improved)
                const sedimentAmount = this.sedimentMap[index];
                newSedimentMap[index] += sedimentAmount * 0.5; // Keep some sediment
                
                // Distribute remaining sediment to neighbors based on velocity
                if (Math.abs(vx) > 0.01) {
                    const targetX = x + Math.sign(vx);
                    if (targetX >= 0 && targetX < width) {
                        const targetIndex = y * width + targetX;
                        newSedimentMap[targetIndex] += sedimentAmount * 0.25;
                    }
                }
                
                if (Math.abs(vy) > 0.01) {
                    const targetY = y + Math.sign(vy);
                    if (targetY >= 0 && targetY < height) {
                        const targetIndex = targetY * width + x;
                        newSedimentMap[targetIndex] += sedimentAmount * 0.25;
                    }
                }
            }
        }
        
        this.sedimentMap = newSedimentMap;
    }
    
    evaporateWater(width, height, rate) {
        for (let i = 0; i < width * height; i++) {
            this.waterMap[i] *= (1 - rate);
            
            // Deposit sediment when water evaporates
            if (this.waterMap[i] < 0.001) {
                // Deposit all sediment when water is nearly gone
                this.sedimentMap[i] = 0;
            }
        }
    }
}
