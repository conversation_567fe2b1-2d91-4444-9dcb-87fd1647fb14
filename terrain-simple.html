<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terrain Synthesis - 3D Procedural Terrain Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
        
        #error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: red;
            font-size: 16px;
            z-index: 200;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="info">
        <h3>Terrain Synthesis</h3>
        <p>WASD: Move camera | Mouse: Look around | Scroll: Zoom</p>
        <p>Use controls panel to adjust terrain parameters</p>
        <p><button onclick="generateTerrain()">Generate New Terrain</button></p>
    </div>
    <div id="controls"></div>
    <div id="loading">Loading terrain generator...</div>
    <div id="error">
        <h3>Error Loading Application</h3>
        <p id="error-message"></p>
        <p>Please check the browser console for more details.</p>
    </div>
    
    <!-- Load Three.js and dat.GUI from CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.9/dat.gui.min.js"></script>
    
    <script>
        // Global variables
        let scene, camera, renderer, terrainMesh, gui;
        let cameraController;
        
        // Terrain parameters
        const params = {
            width: 128,
            height: 128,
            scale: 100,
            noiseScale: 0.02,
            octaves: 4,
            persistence: 0.5,
            lacunarity: 2.0,
            amplitude: 30,
            wireframe: false,
            colorByHeight: true
        };
        
        // Simple Perlin noise implementation
        class SimpleNoise {
            constructor() {
                this.p = [];
                for (let i = 0; i < 256; i++) this.p[i] = Math.floor(Math.random() * 256);
                for (let i = 0; i < 256; i++) this.p[256 + i] = this.p[i];
            }
            
            fade(t) { return t * t * t * (t * (t * 6 - 15) + 10); }
            lerp(a, b, t) { return a + t * (b - a); }
            grad(hash, x, y) {
                const h = hash & 15;
                const u = h < 8 ? x : y;
                const v = h < 4 ? y : h === 12 || h === 14 ? x : 0;
                return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
            }
            
            noise(x, y) {
                const X = Math.floor(x) & 255;
                const Y = Math.floor(y) & 255;
                x -= Math.floor(x);
                y -= Math.floor(y);
                const u = this.fade(x);
                const v = this.fade(y);
                const A = this.p[X] + Y, B = this.p[X + 1] + Y;
                return this.lerp(this.lerp(this.grad(this.p[A], x, y),
                                          this.grad(this.p[B], x - 1, y), u),
                                this.lerp(this.grad(this.p[A + 1], x, y - 1),
                                          this.grad(this.p[B + 1], x - 1, y - 1), u), v);
            }
        }
        
        const noise = new SimpleNoise();
        
        // Initialize the application
        function init() {
            try {
                console.log('Initializing Terrain Synthesis...');
                
                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                scene.fog = new THREE.Fog(0x87CEEB, 100, 1000);
                
                // Create camera
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);
                camera.position.set(0, 50, 100);
                
                // Create renderer
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.getElementById('container').appendChild(renderer.domElement);
                
                // Add lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(100, 100, 50);
                directionalLight.castShadow = true;
                scene.add(directionalLight);
                
                // Setup controls
                setupControls();
                
                // Setup GUI
                setupGUI();
                
                // Generate initial terrain
                generateTerrain();
                
                // Start render loop
                animate();
                
                // Hide loading screen
                document.getElementById('loading').style.display = 'none';
                
                console.log('Terrain Synthesis initialized successfully!');
                
            } catch (error) {
                console.error('Error initializing application:', error);
                showError(error.message);
            }
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }
        
        function setupControls() {
            const keys = {};
            
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
            });
            
            // Simple camera movement
            function updateCamera() {
                const speed = 2;
                if (keys['KeyW']) camera.position.z -= speed;
                if (keys['KeyS']) camera.position.z += speed;
                if (keys['KeyA']) camera.position.x -= speed;
                if (keys['KeyD']) camera.position.x += speed;
                if (keys['Space']) camera.position.y += speed;
                if (keys['ShiftLeft']) camera.position.y -= speed;
            }
            
            cameraController = { update: updateCamera };
        }
        
        function setupGUI() {
            gui = new dat.GUI({ autoPlace: false });
            document.getElementById('controls').appendChild(gui.domElement);
            
            const terrainFolder = gui.addFolder('Terrain');
            terrainFolder.add(params, 'width', 32, 256, 32).onChange(generateTerrain);
            terrainFolder.add(params, 'height', 32, 256, 32).onChange(generateTerrain);
            terrainFolder.add(params, 'scale', 50, 200, 10).onChange(generateTerrain);
            
            const noiseFolder = gui.addFolder('Noise');
            noiseFolder.add(params, 'noiseScale', 0.005, 0.1, 0.005).onChange(generateTerrain);
            noiseFolder.add(params, 'octaves', 1, 8, 1).onChange(generateTerrain);
            noiseFolder.add(params, 'persistence', 0.1, 1.0, 0.1).onChange(generateTerrain);
            noiseFolder.add(params, 'amplitude', 10, 100, 5).onChange(generateTerrain);
            
            const vizFolder = gui.addFolder('Visualization');
            vizFolder.add(params, 'wireframe').onChange(updateVisualization);
            vizFolder.add(params, 'colorByHeight').onChange(generateTerrain);
            
            terrainFolder.open();
            noiseFolder.open();
            vizFolder.open();
        }
        
        function generateTerrain() {
            try {
                console.log('Generating terrain...');
                
                const { width, height, scale, noiseScale, octaves, persistence, amplitude } = params;
                
                // Generate heightmap
                const heightData = new Float32Array(width * height);
                
                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const index = y * width + x;
                        
                        let value = 0;
                        let freq = noiseScale;
                        let amp = amplitude;
                        let maxValue = 0;
                        
                        for (let i = 0; i < octaves; i++) {
                            value += noise.noise(x * freq, y * freq) * amp;
                            maxValue += amp;
                            amp *= persistence;
                            freq *= 2;
                        }
                        
                        heightData[index] = value / maxValue;
                    }
                }
                
                // Create geometry
                const geometry = new THREE.PlaneGeometry(scale, scale, width - 1, height - 1);
                const vertices = geometry.attributes.position.array;
                
                // Apply heights
                for (let i = 0; i < vertices.length; i += 3) {
                    const x = Math.floor((vertices[i] / scale + 0.5) * width);
                    const y = Math.floor((vertices[i + 1] / scale + 0.5) * height);
                    const index = Math.max(0, Math.min(y * width + x, heightData.length - 1));
                    vertices[i + 2] = heightData[index] * scale * 0.3;
                }
                
                // Apply colors if enabled
                if (params.colorByHeight) {
                    const colors = [];
                    for (let i = 0; i < vertices.length; i += 3) {
                        const height = vertices[i + 2] / (scale * 0.3);
                        const normalizedHeight = (height + amplitude) / (amplitude * 2);
                        
                        if (normalizedHeight < 0.3) {
                            colors.push(0.1, 0.3, 0.8); // Blue (water)
                        } else if (normalizedHeight < 0.5) {
                            colors.push(0.8, 0.7, 0.5); // Sandy
                        } else if (normalizedHeight < 0.7) {
                            colors.push(0.2, 0.6, 0.2); // Green
                        } else {
                            colors.push(0.9, 0.9, 0.9); // White (snow)
                        }
                    }
                    geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
                }
                
                geometry.computeVertexNormals();
                
                // Create material
                const material = new THREE.MeshLambertMaterial({
                    vertexColors: params.colorByHeight,
                    wireframe: params.wireframe,
                    color: params.colorByHeight ? 0xffffff : 0x8fbc8f
                });
                
                // Remove existing terrain
                if (terrainMesh) {
                    scene.remove(terrainMesh);
                }
                
                // Create new terrain mesh
                terrainMesh = new THREE.Mesh(geometry, material);
                terrainMesh.rotation.x = -Math.PI / 2;
                terrainMesh.receiveShadow = true;
                terrainMesh.castShadow = true;
                scene.add(terrainMesh);
                
                console.log('Terrain generation complete');
                
            } catch (error) {
                console.error('Error generating terrain:', error);
                alert('Error generating terrain: ' + error.message);
            }
        }
        
        function updateVisualization() {
            if (terrainMesh) {
                terrainMesh.material.wireframe = params.wireframe;
            }
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            if (cameraController) {
                cameraController.update();
            }
            
            renderer.render(scene, camera);
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Error handling
        window.addEventListener('error', function(e) {
            console.error('Application error:', e);
            showError(e.message);
        });
        
        // Check if Three.js loaded and start application
        if (typeof THREE === 'undefined') {
            showError('Failed to load Three.js library. Please check your internet connection.');
        } else {
            // Wait a bit for dat.GUI to load, then start
            setTimeout(() => {
                if (typeof dat === 'undefined') {
                    showError('Failed to load dat.GUI library. Please check your internet connection.');
                } else {
                    init();
                }
            }, 100);
        }
    </script>
</body>
</html>
